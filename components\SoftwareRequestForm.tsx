'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/Button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { AlertCircle, CheckCircle, FileText, Calculator, Users, Building } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface SoftwareRequestFormProps {
  onSubmit: (data: SoftwareRequestData) => Promise<void>
  isLoading?: boolean
  existingRequest?: any
}

interface SoftwareRequestData {
  requestType: 'demo' | 'production'
  studentCount: number
  facultyCount: number
  completeAddress: string
  contactNumber: string
  primaryEmail: string
  class1Fee?: number
  class4Fee?: number
  class6Fee?: number
  class10Fee?: number
  class1112Fee?: number
  termsAccepted?: boolean
  termsVersion?: string
}

interface TermsConditions {
  version: string
  title: string
  content: string
  effectiveDate: string
}

export default function SoftwareRequestForm({ onSubmit, isLoading = false, existingRequest }: SoftwareRequestFormProps) {
  const [requestType, setRequestType] = useState<'demo' | 'production'>('demo')
  const [formData, setFormData] = useState<SoftwareRequestData>({
    requestType: 'demo',
    studentCount: 0,
    facultyCount: 0,
    completeAddress: '',
    contactNumber: '',
    primaryEmail: '',
  })
  const [termsConditions, setTermsConditions] = useState<TermsConditions | null>(null)
  const [showTermsModal, setShowTermsModal] = useState(false)
  const [termsAccepted, setTermsAccepted] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [calculatedAverage, setCalculatedAverage] = useState<number | null>(null)

  // Fetch terms & conditions on component mount
  useEffect(() => {
    fetchTermsConditions()
  }, [])

  // Calculate average fee when fee fields change
  useEffect(() => {
    if (requestType === 'production') {
      const fees = [
        formData.class1Fee,
        formData.class4Fee,
        formData.class6Fee,
        formData.class10Fee,
        formData.class1112Fee
      ].filter(fee => fee && fee > 0) as number[]
      
      if (fees.length >= 2) {
        const average = fees.reduce((sum, fee) => sum + fee, 0) / fees.length
        setCalculatedAverage(average)
      } else {
        setCalculatedAverage(null)
      }
    }
  }, [formData.class1Fee, formData.class4Fee, formData.class6Fee, formData.class10Fee, formData.class1112Fee, requestType])

  const fetchTermsConditions = async () => {
    try {
      const token = localStorage.getItem('authToken')
      const response = await fetch('/api/auth/terms-conditions', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      
      if (response.ok) {
        const terms = await response.json()
        setTermsConditions(terms)
      }
    } catch (error) {
      console.error('Error fetching terms:', error)
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.studentCount || formData.studentCount < 1) {
      newErrors.studentCount = 'Student count must be at least 1'
    }
    if (!formData.facultyCount || formData.facultyCount < 1) {
      newErrors.facultyCount = 'Faculty count must be at least 1'
    }
    if (!formData.completeAddress || formData.completeAddress.length < 10) {
      newErrors.completeAddress = 'Complete address must be at least 10 characters'
    }
    if (!formData.contactNumber || formData.contactNumber.length < 10) {
      newErrors.contactNumber = 'Contact number must be at least 10 digits'
    }
    if (!formData.primaryEmail || !/\S+@\S+\.\S+/.test(formData.primaryEmail)) {
      newErrors.primaryEmail = 'Valid email address is required'
    }

    if (requestType === 'production') {
      const fees = [
        formData.class1Fee,
        formData.class4Fee,
        formData.class6Fee,
        formData.class10Fee,
        formData.class1112Fee
      ].filter(fee => fee && fee > 0)
      
      if (fees.length < 2) {
        newErrors.fees = 'At least 2 class fee structures are required for production requests'
      }
      
      if (!termsAccepted) {
        newErrors.terms = 'You must accept the terms and conditions for production requests'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    const submitData: SoftwareRequestData = {
      ...formData,
      requestType,
      termsAccepted: requestType === 'production' ? termsAccepted : undefined,
      termsVersion: requestType === 'production' && termsConditions ? termsConditions.version : undefined
    }

    await onSubmit(submitData)
  }

  const handleInputChange = (field: keyof SoftwareRequestData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  if (existingRequest) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Existing Software Request
          </CardTitle>
          <CardDescription>
            You already have a software request in progress
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="font-medium">Request Type:</span>
              <Badge variant={existingRequest.requestType === 'demo' ? 'secondary' : 'default'}>
                {existingRequest.requestType.toUpperCase()}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="font-medium">Status:</span>
              <Badge variant={existingRequest.status === 'approved' ? 'default' : 'secondary'}>
                {existingRequest.status.toUpperCase()}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="font-medium">Submitted:</span>
              <span>{new Date(existingRequest.createdAt).toLocaleDateString()}</span>
            </div>
            {existingRequest.reviewNotes && (
              <div>
                <span className="font-medium">Review Notes:</span>
                <p className="text-sm text-gray-600 mt-1">{existingRequest.reviewNotes}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* Request Type Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Request Type</CardTitle>
          <CardDescription>
            Choose between a demo trial or production subscription
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Card 
                className={`cursor-pointer transition-all ${requestType === 'demo' ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:bg-gray-50'}`}
                onClick={() => setRequestType('demo')}
              >
                <CardContent className="p-6">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Users className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold">Demo Request</h3>
                      <p className="text-sm text-gray-600">7-day free trial</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Card 
                className={`cursor-pointer transition-all ${requestType === 'production' ? 'ring-2 ring-green-500 bg-green-50' : 'hover:bg-gray-50'}`}
                onClick={() => setRequestType('production')}
              >
                <CardContent className="p-6">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Building className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold">Production Request</h3>
                      <p className="text-sm text-gray-600">Full subscription</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </CardContent>
      </Card>

      {/* Main Form */}
      <Card>
        <CardHeader>
          <CardTitle>School Information</CardTitle>
          <CardDescription>
            Provide your school's operational details
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="studentCount">Total Students *</Label>
                <Input
                  id="studentCount"
                  type="number"
                  min="1"
                  value={formData.studentCount || ''}
                  onChange={(e) => handleInputChange('studentCount', parseInt(e.target.value) || 0)}
                  className={errors.studentCount ? 'border-red-500' : ''}
                />
                {errors.studentCount && (
                  <p className="text-sm text-red-500 mt-1">{errors.studentCount}</p>
                )}
              </div>

              <div>
                <Label htmlFor="facultyCount">Total Faculty *</Label>
                <Input
                  id="facultyCount"
                  type="number"
                  min="1"
                  value={formData.facultyCount || ''}
                  onChange={(e) => handleInputChange('facultyCount', parseInt(e.target.value) || 0)}
                  className={errors.facultyCount ? 'border-red-500' : ''}
                />
                {errors.facultyCount && (
                  <p className="text-sm text-red-500 mt-1">{errors.facultyCount}</p>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="completeAddress">Complete Address *</Label>
              <Textarea
                id="completeAddress"
                value={formData.completeAddress}
                onChange={(e) => handleInputChange('completeAddress', e.target.value)}
                className={errors.completeAddress ? 'border-red-500' : ''}
                rows={3}
              />
              {errors.completeAddress && (
                <p className="text-sm text-red-500 mt-1">{errors.completeAddress}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="contactNumber">Contact Number *</Label>
                <Input
                  id="contactNumber"
                  type="tel"
                  value={formData.contactNumber}
                  onChange={(e) => handleInputChange('contactNumber', e.target.value)}
                  className={errors.contactNumber ? 'border-red-500' : ''}
                />
                {errors.contactNumber && (
                  <p className="text-sm text-red-500 mt-1">{errors.contactNumber}</p>
                )}
              </div>

              <div>
                <Label htmlFor="primaryEmail">Primary Email *</Label>
                <Input
                  id="primaryEmail"
                  type="email"
                  value={formData.primaryEmail}
                  onChange={(e) => handleInputChange('primaryEmail', e.target.value)}
                  className={errors.primaryEmail ? 'border-red-500' : ''}
                />
                {errors.primaryEmail && (
                  <p className="text-sm text-red-500 mt-1">{errors.primaryEmail}</p>
                )}
              </div>
            </div>

            {/* Fee Structure for Production Requests */}
            {requestType === 'production' && (
              <>
                <Separator />
                <div>
                  <div className="flex items-center gap-2 mb-4">
                    <Calculator className="h-5 w-5" />
                    <h3 className="text-lg font-semibold">Fee Structure</h3>
                  </div>
                  <p className="text-sm text-gray-600 mb-4">
                    Provide fee information for at least 2 classes. This helps us calculate your subscription pricing.
                  </p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="class1Fee">Class 1 Fee (₹)</Label>
                      <Input
                        id="class1Fee"
                        type="number"
                        min="0"
                        value={formData.class1Fee || ''}
                        onChange={(e) => handleInputChange('class1Fee', parseFloat(e.target.value) || undefined)}
                      />
                    </div>

                    <div>
                      <Label htmlFor="class4Fee">Class 4 Fee (₹)</Label>
                      <Input
                        id="class4Fee"
                        type="number"
                        min="0"
                        value={formData.class4Fee || ''}
                        onChange={(e) => handleInputChange('class4Fee', parseFloat(e.target.value) || undefined)}
                      />
                    </div>

                    <div>
                      <Label htmlFor="class6Fee">Class 6 Fee (₹)</Label>
                      <Input
                        id="class6Fee"
                        type="number"
                        min="0"
                        value={formData.class6Fee || ''}
                        onChange={(e) => handleInputChange('class6Fee', parseFloat(e.target.value) || undefined)}
                      />
                    </div>

                    <div>
                      <Label htmlFor="class10Fee">Class 10 Fee (₹)</Label>
                      <Input
                        id="class10Fee"
                        type="number"
                        min="0"
                        value={formData.class10Fee || ''}
                        onChange={(e) => handleInputChange('class10Fee', parseFloat(e.target.value) || undefined)}
                      />
                    </div>

                    <div>
                      <Label htmlFor="class1112Fee">Class 11-12 Fee (₹)</Label>
                      <Input
                        id="class1112Fee"
                        type="number"
                        min="0"
                        value={formData.class1112Fee || ''}
                        onChange={(e) => handleInputChange('class1112Fee', parseFloat(e.target.value) || undefined)}
                      />
                    </div>
                  </div>

                  {calculatedAverage && (
                    <Alert className="mt-4">
                      <Calculator className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Calculated Average Fee: ₹{calculatedAverage.toFixed(2)}</strong>
                        <br />
                        Your subscription will be based on this average fee structure.
                      </AlertDescription>
                    </Alert>
                  )}

                  {errors.fees && (
                    <p className="text-sm text-red-500 mt-2">{errors.fees}</p>
                  )}
                </div>

                {/* Terms & Conditions */}
                <Separator />
                <div>
                  <div className="flex items-center gap-2 mb-4">
                    <FileText className="h-5 w-5" />
                    <h3 className="text-lg font-semibold">Terms & Conditions</h3>
                  </div>
                  
                  {termsConditions && (
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="termsAccepted"
                          checked={termsAccepted}
                          onChange={(e) => setTermsAccepted(e.target.checked)}
                          className="rounded border-gray-300"
                        />
                        <Label htmlFor="termsAccepted" className="text-sm">
                          I accept the{' '}
                          <button
                            type="button"
                            onClick={() => setShowTermsModal(true)}
                            className="text-blue-600 hover:underline"
                          >
                            Terms & Conditions ({termsConditions.version})
                          </button>
                        </Label>
                      </div>
                      
                      {errors.terms && (
                        <p className="text-sm text-red-500">{errors.terms}</p>
                      )}
                    </div>
                  )}
                </div>
              </>
            )}

            {/* Submit Button */}
            <div className="flex justify-end">
              <Button 
                type="submit" 
                disabled={isLoading}
                className="min-w-[150px]"
              >
                {isLoading ? 'Submitting...' : `Submit ${requestType === 'demo' ? 'Demo' : 'Production'} Request`}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Terms Modal */}
      {showTermsModal && termsConditions && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl max-h-[80vh] overflow-hidden">
            <div className="p-6 border-b">
              <h2 className="text-xl font-semibold">{termsConditions.title}</h2>
              <p className="text-sm text-gray-600">Version {termsConditions.version}</p>
            </div>
            <div className="p-6 overflow-y-auto max-h-[60vh]">
              <pre className="whitespace-pre-wrap text-sm">{termsConditions.content}</pre>
            </div>
            <div className="p-6 border-t flex justify-end gap-3">
              <Button variant="outline" onClick={() => setShowTermsModal(false)}>
                Close
              </Button>
              <Button 
                onClick={() => {
                  setTermsAccepted(true)
                  setShowTermsModal(false)
                }}
              >
                Accept Terms
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
