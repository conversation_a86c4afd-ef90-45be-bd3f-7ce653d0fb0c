import { <PERSON>o } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { Resend } from 'resend'
import { db } from '@/src/db'
import { clients, clientUsers, partners, referralCodes, schoolReferrals, softwareRequests, requestStatusHistory, termsConditions } from '@/src/db/schema'
import { eq, and, desc } from 'drizzle-orm'

const app = new Hono()

// Initialize Resend
const resend = new Resend(process.env.RESEND_API_KEY)

// Validation schemas
const registerSchema = z.object({
  schoolName: z.string().min(2, 'School name must be at least 2 characters'),
  contactPerson: z.string().min(2, 'Contact person name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 digits'),
  address: z.string().min(10, 'Address must be at least 10 characters'),
  estimatedStudents: z.number().min(1, 'Must have at least 1 student'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string(),
  referralCode: z.string().optional()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required')
})

const verifyOtpSchema = z.object({
  email: z.string().email('Invalid email address'),
  otp: z.string().length(6, 'OTP must be 6 digits')
})

const resendOtpSchema = z.object({
  email: z.string().email('Invalid email address')
})

// Helper function to generate OTP
function generateOTP(): string {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

// Helper function to send OTP email
async function sendOTPEmail(email: string, otp: string, name: string) {
  try {
    await resend.emails.send({
      from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
      to: [email],
      subject: 'Verify Your Schopio Account - OTP Code',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin: 0;">Schopio</h1>
            <p style="color: #64748b; margin: 5px 0;">School Management Platform</p>
          </div>
          
          <div style="background: #f8fafc; padding: 30px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #1e293b; margin-top: 0;">Verify Your Account</h2>
            <p style="color: #475569; margin-bottom: 20px;">Hi ${name},</p>
            <p style="color: #475569; margin-bottom: 20px;">
              Thank you for registering with Schopio! Please use the following OTP code to verify your email address:
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <div style="background: #2563eb; color: white; font-size: 32px; font-weight: bold; padding: 20px; border-radius: 8px; letter-spacing: 8px; display: inline-block;">
                ${otp}
              </div>
            </div>
            
            <p style="color: #475569; margin-bottom: 10px;">
              This OTP will expire in <strong>10 minutes</strong>.
            </p>
            <p style="color: #475569; margin-bottom: 0;">
              If you didn't request this verification, please ignore this email.
            </p>
          </div>
          
          <div style="text-align: center; color: #94a3b8; font-size: 14px;">
            <p>© 2024 Schopio. All rights reserved.</p>
            <p>This is an automated email. Please do not reply.</p>
          </div>
        </div>
      `
    })
    return true
  } catch (error) {
    console.error('Error sending OTP email:', error)
    return false
  }
}

// Helper function to generate JWT token
function generateToken(userId: string, email: string): string {
  return jwt.sign(
    { userId, email },
    process.env.JWT_SECRET || 'fallback-secret-key',
    { expiresIn: '7d' }
  )
}

// Register endpoint
app.post('/register', zValidator('json', registerSchema, (result, c) => {
  if (!result.success) {
    console.log('Validation errors:', result.error.issues)
    return c.json({
      error: 'Validation failed',
      details: result.error.issues.map(issue => ({
        field: issue.path.join('.'),
        message: issue.message
      }))
    }, 400)
  }
}), async (c) => {
  try {
    const data = c.req.valid('json')
    console.log('Registration data received:', data)

    // Check if email already exists
    const existingUser = await db.select().from(clientUsers).where(eq(clientUsers.email, data.email)).limit(1)
    if (existingUser.length > 0) {
      return c.json({ error: 'Email already registered' }, 400)
    }

    // Hash password
    const passwordHash = await bcrypt.hash(data.password, 12)
    
    // Generate OTP
    const otp = generateOTP()
    const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes

    // Validate referral code if provided
    let referralCodeData = null
    if (data.referralCode && data.referralCode.trim()) {
      const [referralCode] = await db
        .select({
          id: referralCodes.id,
          partnerId: referralCodes.partnerId,
          code: referralCodes.code,
          isActive: referralCodes.isActive,
          usageCount: referralCodes.usageCount,
          maxUsage: referralCodes.maxUsage
        })
        .from(referralCodes)
        .innerJoin(partners, eq(referralCodes.partnerId, partners.id))
        .where(and(
          eq(referralCodes.code, data.referralCode.trim().toUpperCase()),
          eq(referralCodes.isActive, true),
          eq(partners.isActive, true)
        ))
        .limit(1)

      if (!referralCode) {
        return c.json({ error: 'Invalid or inactive referral code' }, 400)
      }

      // Check usage limit
      if (referralCode.maxUsage && (referralCode.usageCount || 0) >= referralCode.maxUsage) {
        return c.json({ error: 'Referral code has reached maximum usage limit' }, 400)
      }

      referralCodeData = referralCode
    }

    // Get client IP and user agent for referral tracking
    const ipAddress = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
    const userAgent = c.req.header('user-agent') || 'unknown'

    // Use transaction to ensure data consistency
    const result = await db.transaction(async (tx) => {
      // Create client record first
      const [newClient] = await tx.insert(clients).values({
        schoolName: data.schoolName,
        schoolCode: `SCH${Date.now()}`, // Generate unique school code
        email: data.email,
        phone: data.phone,
        address: data.address,
        contactPerson: data.contactPerson,
        actualStudentCount: data.estimatedStudents,
        estimatedStudentCount: data.estimatedStudents,
        onboardingStatus: 'pending',
        status: 'active'
      }).returning()

      // Create user record
      const [newUser] = await tx.insert(clientUsers).values({
        clientId: newClient.id,
        email: data.email,
        passwordHash,
        name: data.contactPerson,
        role: 'admin',
        isActive: true,
        emailVerified: false,
        otpCode: otp,
        otpExpiresAt
      }).returning()

      // Apply referral code if provided
      if (referralCodeData) {
        // Create school referral record
        await tx.insert(schoolReferrals).values({
          clientId: newClient.id,
          partnerId: referralCodeData.partnerId,
          referralCodeId: referralCodeData.id,
          referralSource: 'registration',
          ipAddress: ipAddress,
          userAgent: userAgent,
          appliedBy: newUser.id
        })

        // Update referral code usage count
        await tx
          .update(referralCodes)
          .set({
            usageCount: (referralCodeData.usageCount || 0) + 1
          })
          .where(eq(referralCodes.id, referralCodeData.id))
      }

      return { newClient, newUser }
    })

    const { newClient, newUser } = result

    // Send OTP email
    const emailSent = await sendOTPEmail(data.email, otp, data.contactPerson)
    
    if (!emailSent) {
      // Rollback - delete created records
      await db.delete(clientUsers).where(eq(clientUsers.id, newUser.id))
      await db.delete(clients).where(eq(clients.id, newClient.id))
      return c.json({ error: 'Failed to send verification email. Please try again.' }, 500)
    }

    return c.json({
      message: 'Registration successful! Please check your email for the OTP code.',
      email: data.email,
      requiresVerification: true
    })

  } catch (error) {
    console.error('Registration error:', error)
    return c.json({ error: 'Registration failed. Please try again.' }, 500)
  }
})

// Login endpoint
app.post('/login', zValidator('json', loginSchema), async (c) => {
  try {
    const { email, password } = c.req.valid('json')
    
    // Find user
    const [user] = await db.select().from(clientUsers).where(eq(clientUsers.email, email)).limit(1)
    
    if (!user) {
      return c.json({ error: 'Invalid email or password' }, 401)
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.passwordHash)
    if (!isValidPassword) {
      return c.json({ error: 'Invalid email or password' }, 401)
    }

    // Check if email is verified
    if (!user.emailVerified) {
      // Generate new OTP and send
      const otp = generateOTP()
      const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000)
      
      await db.update(clientUsers)
        .set({ otpCode: otp, otpExpiresAt })
        .where(eq(clientUsers.id, user.id))
      
      await sendOTPEmail(email, otp, user.name)
      
      return c.json({
        error: 'Email not verified. A new OTP has been sent to your email.',
        requiresVerification: true,
        email
      }, 403)
    }

    // Check if account is active
    if (!user.isActive) {
      return c.json({ error: 'Account is deactivated. Please contact support.' }, 403)
    }

    // Update last login
    await db.update(clientUsers)
      .set({ lastLogin: new Date() })
      .where(eq(clientUsers.id, user.id))

    // Generate token
    const token = generateToken(user.id, user.email)

    return c.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      }
    })

  } catch (error) {
    console.error('Login error:', error)
    return c.json({ error: 'Login failed. Please try again.' }, 500)
  }
})

// Verify OTP endpoint
app.post('/verify-otp', zValidator('json', verifyOtpSchema), async (c) => {
  try {
    const { email, otp } = c.req.valid('json')
    
    // Find user
    const [user] = await db.select().from(clientUsers).where(eq(clientUsers.email, email)).limit(1)
    
    if (!user) {
      return c.json({ error: 'User not found' }, 404)
    }

    // Check OTP
    if (!user.otpCode || user.otpCode !== otp) {
      return c.json({ error: 'Invalid OTP code' }, 400)
    }

    // Check OTP expiry
    if (!user.otpExpiresAt || new Date() > user.otpExpiresAt) {
      return c.json({ error: 'OTP has expired. Please request a new one.' }, 400)
    }

    // Verify email and clear OTP
    await db.update(clientUsers)
      .set({ 
        emailVerified: true, 
        otpCode: null, 
        otpExpiresAt: null,
        lastLogin: new Date()
      })
      .where(eq(clientUsers.id, user.id))

    // Generate token
    const token = generateToken(user.id, user.email)

    return c.json({
      message: 'Email verified successfully',
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      }
    })

  } catch (error) {
    console.error('OTP verification error:', error)
    return c.json({ error: 'Verification failed. Please try again.' }, 500)
  }
})

// Resend OTP endpoint
app.post('/resend-otp', zValidator('json', resendOtpSchema), async (c) => {
  try {
    const { email } = c.req.valid('json')

    // Find user
    const [user] = await db.select().from(clientUsers).where(eq(clientUsers.email, email)).limit(1)

    if (!user) {
      return c.json({ error: 'User not found' }, 404)
    }

    if (user.emailVerified) {
      return c.json({ error: 'Email is already verified' }, 400)
    }

    // Generate new OTP
    const otp = generateOTP()
    const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000)

    await db.update(clientUsers)
      .set({ otpCode: otp, otpExpiresAt })
      .where(eq(clientUsers.id, user.id))

    // Send OTP email
    const emailSent = await sendOTPEmail(email, otp, user.name)

    if (!emailSent) {
      return c.json({ error: 'Failed to send OTP email. Please try again.' }, 500)
    }

    return c.json({
      message: 'New OTP sent to your email'
    })

  } catch (error) {
    console.error('Resend OTP error:', error)
    return c.json({ error: 'Failed to resend OTP. Please try again.' }, 500)
  }
})

// Get user profile endpoint (protected)
app.get('/profile', async (c) => {
  try {
    const authHeader = c.req.header('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any

    if (!decoded.userId) {
      return c.json({ error: 'Invalid token' }, 401)
    }

    // Get user with client data
    const [user] = await db
      .select({
        id: clientUsers.id,
        email: clientUsers.email,
        name: clientUsers.name,
        role: clientUsers.role,
        isActive: clientUsers.isActive,
        emailVerified: clientUsers.emailVerified,
        lastLogin: clientUsers.lastLogin,
        createdAt: clientUsers.createdAt,
        clientId: clientUsers.clientId,
        schoolName: clients.schoolName,
        schoolCode: clients.schoolCode,
        phone: clients.phone,
        address: clients.address,
        contactPerson: clients.contactPerson,
        actualStudentCount: clients.actualStudentCount,
        onboardingStatus: clients.onboardingStatus,
        status: clients.status,
        clientCreatedAt: clients.createdAt
      })
      .from(clientUsers)
      .leftJoin(clients, eq(clientUsers.clientId, clients.id))
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user) {
      return c.json({ error: 'User not found' }, 404)
    }

    if (!user.isActive) {
      return c.json({ error: 'Account is deactivated' }, 403)
    }

    return c.json({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        emailVerified: user.emailVerified,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt
      },
      client: {
        id: user.clientId,
        schoolName: user.schoolName,
        schoolCode: user.schoolCode,
        phone: user.phone,
        address: user.address,
        contactPerson: user.contactPerson,
        actualStudentCount: user.actualStudentCount,
        onboardingStatus: user.onboardingStatus,
        status: user.status,
        createdAt: user.clientCreatedAt
      }
    })

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }

    console.error('Profile fetch error:', error)
    return c.json({ error: 'Failed to fetch profile' }, 500)
  }
})

// Update user profile endpoint (protected)
const updateUserProfileSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  phone: z.string().min(10, 'Phone number must be at least 10 digits').optional(),
})

app.put('/profile/user', zValidator('json', updateUserProfileSchema), async (c) => {
  try {
    const authHeader = c.req.header('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any

    if (!decoded.userId) {
      return c.json({ error: 'Invalid token' }, 401)
    }

    const updates = c.req.valid('json')

    // Update user profile
    const [updatedUser] = await db
      .update(clientUsers)
      .set({
        ...updates,
        updatedAt: new Date()
      })
      .where(eq(clientUsers.id, decoded.userId))
      .returning()

    if (!updatedUser) {
      return c.json({ error: 'User not found' }, 404)
    }

    return c.json({
      success: true,
      message: 'Profile updated successfully',
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        name: updatedUser.name,
        role: updatedUser.role,
        emailVerified: updatedUser.emailVerified,
        lastLogin: updatedUser.lastLogin,
        createdAt: updatedUser.createdAt
      }
    })

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    console.error('Error updating user profile:', error)
    return c.json({ error: 'Failed to update profile' }, 500)
  }
})

// Update school information endpoint (protected)
const updateSchoolInfoSchema = z.object({
  schoolName: z.string().min(2, 'School name must be at least 2 characters').optional(),
  address: z.string().min(10, 'Address must be at least 10 characters').optional(),
  contactPerson: z.string().min(2, 'Contact person name must be at least 2 characters').optional(),
  actualStudentCount: z.number().min(1, 'Must have at least 1 student').optional(),
})

app.put('/profile/school', zValidator('json', updateSchoolInfoSchema), async (c) => {
  try {
    const authHeader = c.req.header('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any

    if (!decoded.userId) {
      return c.json({ error: 'Invalid token' }, 401)
    }

    // Get user's client ID
    const [user] = await db
      .select({ clientId: clientUsers.clientId })
      .from(clientUsers)
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user || !user.clientId) {
      return c.json({ error: 'User or school not found' }, 404)
    }

    const updates = c.req.valid('json')

    // Update school information
    const [updatedClient] = await db
      .update(clients)
      .set({
        ...updates,
        updatedAt: new Date()
      })
      .where(eq(clients.id, user.clientId))
      .returning()

    if (!updatedClient) {
      return c.json({ error: 'School not found' }, 404)
    }

    return c.json({
      success: true,
      message: 'School information updated successfully',
      client: {
        id: updatedClient.id,
        schoolName: updatedClient.schoolName,
        schoolCode: updatedClient.schoolCode,
        phone: updatedClient.phone,
        address: updatedClient.address,
        contactPerson: updatedClient.contactPerson,
        actualStudentCount: updatedClient.actualStudentCount,
        onboardingStatus: updatedClient.onboardingStatus,
        status: updatedClient.status,
        createdAt: updatedClient.createdAt
      }
    })

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    console.error('Error updating school information:', error)
    return c.json({ error: 'Failed to update school information' }, 500)
  }
})

// Change password endpoint (protected)
const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, 'New password must be at least 8 characters'),
  confirmPassword: z.string().min(1, 'Password confirmation is required')
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "New passwords don't match",
  path: ["confirmPassword"],
})

app.put('/profile/password', zValidator('json', changePasswordSchema), async (c) => {
  try {
    const authHeader = c.req.header('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any

    if (!decoded.userId) {
      return c.json({ error: 'Invalid token' }, 401)
    }

    const { currentPassword, newPassword } = c.req.valid('json')

    // Get current user
    const [user] = await db
      .select({ passwordHash: clientUsers.passwordHash })
      .from(clientUsers)
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user) {
      return c.json({ error: 'User not found' }, 404)
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash)
    if (!isCurrentPasswordValid) {
      return c.json({ error: 'Current password is incorrect' }, 400)
    }

    // Hash new password
    const newPasswordHash = await bcrypt.hash(newPassword, 12)

    // Update password
    await db
      .update(clientUsers)
      .set({
        passwordHash: newPasswordHash,
        updatedAt: new Date()
      })
      .where(eq(clientUsers.id, decoded.userId))

    return c.json({
      success: true,
      message: 'Password changed successfully'
    })

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    console.error('Error changing password:', error)
    return c.json({ error: 'Failed to change password' }, 500)
  }
})

// Resend email verification endpoint (protected)
app.post('/profile/resend-verification', async (c) => {
  try {
    const authHeader = c.req.header('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any

    if (!decoded.userId) {
      return c.json({ error: 'Invalid token' }, 401)
    }

    // Get user details
    const [user] = await db
      .select({ email: clientUsers.email, name: clientUsers.name, emailVerified: clientUsers.emailVerified })
      .from(clientUsers)
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user) {
      return c.json({ error: 'User not found' }, 404)
    }

    if (user.emailVerified) {
      return c.json({ error: 'Email is already verified' }, 400)
    }

    // Generate new OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString()
    const otpExpiry = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes

    // Update user with new OTP
    await db
      .update(clientUsers)
      .set({
        otpCode: otp,
        otpExpiresAt: otpExpiry,
        updatedAt: new Date()
      })
      .where(eq(clientUsers.id, decoded.userId))

    // Send verification email
    const resend = new Resend(process.env.RESEND_API_KEY)

    await resend.emails.send({
      from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
      to: user.email,
      subject: 'Verify Your Email - Schopio',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">Email Verification</h2>
          <p>Hello ${user.name},</p>
          <p>Please use the following OTP to verify your email address:</p>
          <div style="background: #f3f4f6; padding: 20px; text-align: center; margin: 20px 0;">
            <h1 style="color: #1f2937; font-size: 32px; margin: 0; letter-spacing: 5px;">${otp}</h1>
          </div>
          <p>This OTP will expire in 10 minutes.</p>
          <p>If you didn't request this verification, please ignore this email.</p>
          <hr style="margin: 30px 0;">
          <p style="color: #6b7280; font-size: 14px;">
            Best regards,<br>
            The Schopio Team
          </p>
        </div>
      `
    })

    return c.json({
      success: true,
      message: 'Verification email sent successfully'
    })

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    console.error('Error sending verification email:', error)
    return c.json({ error: 'Failed to send verification email' }, 500)
  }
})

// Validate referral code endpoint (public)
app.post('/referral/validate', async (c) => {
  try {
    const { code } = await c.req.json()

    if (!code || typeof code !== 'string') {
      return c.json({ error: 'Referral code is required' }, 400)
    }

    // Check if referral code exists and is active
    const [referralCode] = await db
      .select({
        id: referralCodes.id,
        code: referralCodes.code,
        partnerId: referralCodes.partnerId,
        isActive: referralCodes.isActive,
        usageCount: referralCodes.usageCount,
        maxUsage: referralCodes.maxUsage,
        partnerName: partners.name,
        partnerCompany: partners.companyName
      })
      .from(referralCodes)
      .innerJoin(partners, eq(referralCodes.partnerId, partners.id))
      .where(and(
        eq(referralCodes.code, code.toUpperCase()),
        eq(referralCodes.isActive, true),
        eq(partners.isActive, true)
      ))
      .limit(1)

    if (!referralCode) {
      return c.json({
        valid: false,
        error: 'Invalid or inactive referral code'
      }, 400)
    }

    // Check if referral code has reached max usage
    if (referralCode.maxUsage && (referralCode.usageCount || 0) >= referralCode.maxUsage) {
      return c.json({
        valid: false,
        error: 'Referral code has reached maximum usage limit'
      }, 400)
    }

    return c.json({
      valid: true,
      referralCode: {
        id: referralCode.id,
        code: referralCode.code,
        partnerName: referralCode.partnerName,
        partnerCompany: referralCode.partnerCompany,
        usageCount: referralCode.usageCount,
        maxUsage: referralCode.maxUsage
      }
    })

  } catch (error) {
    console.error('Error validating referral code:', error)
    return c.json({ error: 'Failed to validate referral code' }, 500)
  }
})

// Apply referral code to school (protected)
app.post('/referral/apply', async (c) => {
  try {
    const authHeader = c.req.header('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any

    if (!decoded.userId) {
      return c.json({ error: 'Invalid token' }, 401)
    }

    const { code } = await c.req.json()

    if (!code || typeof code !== 'string') {
      return c.json({ error: 'Referral code is required' }, 400)
    }

    // Get user and client information
    const [user] = await db
      .select({
        id: clientUsers.id,
        clientId: clientUsers.clientId,
        email: clientUsers.email
      })
      .from(clientUsers)
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user || !user.clientId) {
      return c.json({ error: 'User or school not found' }, 404)
    }

    // Check if school already has a referral
    const [existingReferral] = await db
      .select({ id: schoolReferrals.id })
      .from(schoolReferrals)
      .where(and(
        eq(schoolReferrals.clientId, user.clientId),
        eq(schoolReferrals.isActive, true)
      ))
      .limit(1)

    if (existingReferral) {
      return c.json({ error: 'School already has an active referral code applied' }, 400)
    }

    // Validate referral code
    const [referralCode] = await db
      .select({
        id: referralCodes.id,
        partnerId: referralCodes.partnerId,
        code: referralCodes.code,
        isActive: referralCodes.isActive,
        usageCount: referralCodes.usageCount,
        maxUsage: referralCodes.maxUsage,
        partnerName: partners.name,
        partnerCompany: partners.companyName
      })
      .from(referralCodes)
      .innerJoin(partners, eq(referralCodes.partnerId, partners.id))
      .where(and(
        eq(referralCodes.code, code.toUpperCase()),
        eq(referralCodes.isActive, true),
        eq(partners.isActive, true)
      ))
      .limit(1)

    if (!referralCode) {
      return c.json({ error: 'Invalid or inactive referral code' }, 400)
    }

    // Check usage limit
    if (referralCode.maxUsage && (referralCode.usageCount || 0) >= referralCode.maxUsage) {
      return c.json({ error: 'Referral code has reached maximum usage limit' }, 400)
    }

    // Get client IP and user agent
    const ipAddress = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
    const userAgent = c.req.header('user-agent') || 'unknown'

    // Apply referral code
    await db.transaction(async (tx) => {
      // Create school referral record
      await tx.insert(schoolReferrals).values({
        clientId: user.clientId!,
        partnerId: referralCode.partnerId,
        referralCodeId: referralCode.id,
        referralSource: 'profile_update',
        ipAddress: ipAddress,
        userAgent: userAgent,
        appliedBy: user.id
      })

      // Update referral code usage count
      await tx
        .update(referralCodes)
        .set({
          usageCount: (referralCode.usageCount || 0) + 1
        })
        .where(eq(referralCodes.id, referralCode.id))
    })

    return c.json({
      success: true,
      message: 'Referral code applied successfully',
      referral: {
        code: referralCode.code,
        partnerName: referralCode.partnerName,
        partnerCompany: referralCode.partnerCompany,
        appliedAt: new Date().toISOString()
      }
    })

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    console.error('Error applying referral code:', error)
    return c.json({ error: 'Failed to apply referral code' }, 500)
  }
})

// Get school referral status (protected)
app.get('/referral/status', async (c) => {
  try {
    const authHeader = c.req.header('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any

    if (!decoded.userId) {
      return c.json({ error: 'Invalid token' }, 401)
    }

    // Get user and client information
    const [user] = await db
      .select({
        id: clientUsers.id,
        clientId: clientUsers.clientId
      })
      .from(clientUsers)
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user || !user.clientId) {
      return c.json({ error: 'User or school not found' }, 404)
    }

    // Check if school has a referral
    const [referral] = await db
      .select({
        id: schoolReferrals.id,
        referralCode: referralCodes.code,
        partnerName: partners.name,
        partnerCompany: partners.companyName,
        referredAt: schoolReferrals.referredAt,
        referralSource: schoolReferrals.referralSource,
        isActive: schoolReferrals.isActive,
        verifiedAt: schoolReferrals.verifiedAt
      })
      .from(schoolReferrals)
      .innerJoin(referralCodes, eq(schoolReferrals.referralCodeId, referralCodes.id))
      .innerJoin(partners, eq(schoolReferrals.partnerId, partners.id))
      .where(and(
        eq(schoolReferrals.clientId, user.clientId),
        eq(schoolReferrals.isActive, true)
      ))
      .limit(1)

    if (!referral) {
      return c.json({
        hasReferral: false,
        message: 'No referral code applied'
      })
    }

    return c.json({
      hasReferral: true,
      referral: {
        code: referral.referralCode,
        partnerName: referral.partnerName,
        partnerCompany: referral.partnerCompany,
        appliedAt: referral.referredAt,
        source: referral.referralSource,
        isVerified: !!referral.verifiedAt,
        verifiedAt: referral.verifiedAt
      }
    })

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    console.error('Error getting referral status:', error)
    return c.json({ error: 'Failed to get referral status' }, 500)
  }
})

// ===== SOFTWARE REQUEST ENDPOINTS =====

// Software request validation schemas
const softwareRequestSchema = z.object({
  requestType: z.enum(['demo', 'production'], { required_error: 'Request type is required' }),
  studentCount: z.number().min(1, 'Student count must be at least 1'),
  facultyCount: z.number().min(1, 'Faculty count must be at least 1'),
  completeAddress: z.string().min(10, 'Complete address must be at least 10 characters'),
  contactNumber: z.string().min(10, 'Contact number must be at least 10 digits'),
  primaryEmail: z.string().email('Invalid email address'),

  // Monthly fee structure (required for production requests)
  class1Fee: z.number().optional(),
  class4Fee: z.number().optional(),
  class6Fee: z.number().optional(),
  class10Fee: z.number().optional(),
  class1112Fee: z.number().optional(),

  // Terms acceptance (required for production requests)
  termsAccepted: z.boolean().optional(),
  termsVersion: z.string().optional(),
}).refine((data) => {
  // For production requests, require fee structure and terms acceptance
  if (data.requestType === 'production') {
    const fees = [data.class1Fee, data.class4Fee, data.class6Fee, data.class10Fee, data.class1112Fee]
    const validFees = fees.filter(fee => fee && fee > 0)

    return validFees.length >= 2 && data.termsAccepted === true && data.termsVersion
  }
  return true
}, {
  message: "Production requests require at least 2 monthly fee structures and terms acceptance",
  path: ["requestType"],
})

// Helper function to calculate average monthly fee
const calculateAverageFee = (fees: { [key: string]: number | undefined }) => {
  const validFees = Object.values(fees).filter(fee => fee && fee > 0) as number[]
  if (validFees.length === 0) return null
  return validFees.reduce((sum, fee) => sum + fee, 0) / validFees.length
}

// Create software request endpoint
app.post('/software-request', zValidator('json', softwareRequestSchema), async (c) => {
  try {
    const authHeader = c.req.header('Authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any

    // Get user details
    const [user] = await db
      .select()
      .from(clientUsers)
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user || !user.clientId) {
      return c.json({ error: 'User not found or not associated with a school' }, 404)
    }

    const data = c.req.valid('json')

    // Check if user already has a pending or active request
    const existingRequest = await db
      .select()
      .from(softwareRequests)
      .where(and(
        eq(softwareRequests.clientId, user.clientId),
        eq(softwareRequests.status, 'pending')
      ))
      .limit(1)

    if (existingRequest.length > 0) {
      return c.json({ error: 'You already have a pending software request' }, 400)
    }

    // Calculate average monthly fee for production requests
    let calculatedAverageFee = null
    if (data.requestType === 'production') {
      calculatedAverageFee = calculateAverageFee({
        class1Fee: data.class1Fee,
        class4Fee: data.class4Fee,
        class6Fee: data.class6Fee,
        class10Fee: data.class10Fee,
        class1112Fee: data.class1112Fee,
      })
    }

    // Get client IP and user agent
    const ipAddress = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
    const userAgent = c.req.header('user-agent') || 'unknown'

    // Create software request
    const [newRequest] = await db.insert(softwareRequests).values({
      clientId: user.clientId,
      requestType: data.requestType,
      studentCount: data.studentCount,
      facultyCount: data.facultyCount,
      completeAddress: data.completeAddress,
      contactNumber: data.contactNumber,
      primaryEmail: data.primaryEmail,
      class1Fee: data.class1Fee ? data.class1Fee.toString() : null,
      class4Fee: data.class4Fee ? data.class4Fee.toString() : null,
      class6Fee: data.class6Fee ? data.class6Fee.toString() : null,
      class10Fee: data.class10Fee ? data.class10Fee.toString() : null,
      class1112Fee: data.class1112Fee ? data.class1112Fee.toString() : null,
      calculatedAverageFee: calculatedAverageFee ? calculatedAverageFee.toString() : null,
      termsAccepted: data.termsAccepted || false,
      termsAcceptedAt: data.termsAccepted ? new Date() : null,
      termsVersion: data.termsVersion || null,
      ipAddress: ipAddress,
      userAgent: userAgent,
      status: 'pending'
    }).returning()

    // Create status history entry
    await db.insert(requestStatusHistory).values({
      requestId: newRequest.id,
      fromStatus: null,
      toStatus: 'pending',
      changedBy: user.id,
      changeReason: 'Initial request submission',
      metadata: { requestType: data.requestType }
    })

    return c.json({
      message: 'Software request submitted successfully',
      requestId: newRequest.id,
      requestType: data.requestType,
      status: 'pending'
    })

  } catch (error: any) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    console.error('Error creating software request:', error)
    return c.json({ error: 'Failed to create software request' }, 500)
  }
})

// Get software request status endpoint
app.get('/software-request/status', async (c) => {
  try {
    const authHeader = c.req.header('Authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any

    // Get user details
    const [user] = await db
      .select()
      .from(clientUsers)
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user || !user.clientId) {
      return c.json({ error: 'User not found or not associated with a school' }, 404)
    }

    // Get latest software request for this client
    const [request] = await db
      .select()
      .from(softwareRequests)
      .where(eq(softwareRequests.clientId, user.clientId))
      .orderBy(desc(softwareRequests.createdAt))
      .limit(1)

    if (!request) {
      return c.json({ hasRequest: false })
    }

    // Get status history
    const statusHistory = await db
      .select()
      .from(requestStatusHistory)
      .where(eq(requestStatusHistory.requestId, request.id))
      .orderBy(requestStatusHistory.createdAt)

    return c.json({
      hasRequest: true,
      request: {
        id: request.id,
        requestType: request.requestType,
        status: request.status,
        studentCount: request.studentCount,
        facultyCount: request.facultyCount,
        completeAddress: request.completeAddress,
        contactNumber: request.contactNumber,
        primaryEmail: request.primaryEmail,
        calculatedAverageFee: request.calculatedAverageFee,
        termsAccepted: request.termsAccepted,
        termsVersion: request.termsVersion,
        createdAt: request.createdAt,
        approvedAt: request.approvedAt,
        activatedAt: request.activatedAt,
        reviewNotes: request.reviewNotes,
        rejectionReason: request.rejectionReason
      },
      statusHistory: statusHistory.map(history => ({
        fromStatus: history.fromStatus,
        toStatus: history.toStatus,
        changeReason: history.changeReason,
        createdAt: history.createdAt
      })),
      canUpgrade: request.requestType === 'demo' &&
                 (request.status === 'activated' || request.status === 'approved')
    })

  } catch (error: any) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    console.error('Error getting software request status:', error)
    return c.json({ error: 'Failed to get software request status' }, 500)
  }
})

// Upgrade demo to production endpoint
const upgradeToProductionSchema = z.object({
  // Monthly fee structure (required for production requests)
  class1Fee: z.number().min(0, 'Class 1 monthly fee must be a positive number'),
  class4Fee: z.number().min(0, 'Class 4 monthly fee must be a positive number'),
  class6Fee: z.number().min(0, 'Class 6 monthly fee must be a positive number'),
  class10Fee: z.number().min(0, 'Class 10 monthly fee must be a positive number'),
  class1112Fee: z.number().min(0, 'Class 11-12 monthly fee must be a positive number'),
  termsAccepted: z.boolean().refine(val => val === true, 'Terms and conditions must be accepted'),
  termsVersion: z.string().min(1, 'Terms version is required')
})

app.post('/software-request/upgrade-to-production', zValidator('json', upgradeToProductionSchema), async (c) => {
  try {
    const authHeader = c.req.header('Authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any
    const data = c.req.valid('json')

    // Get user details
    const [user] = await db
      .select()
      .from(clientUsers)
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user || !user.clientId) {
      return c.json({ error: 'User not found or not associated with a school' }, 404)
    }

    // Check for existing demo request
    const [existingRequest] = await db
      .select()
      .from(softwareRequests)
      .where(
        and(
          eq(softwareRequests.clientId, user.clientId),
          eq(softwareRequests.requestType, 'demo')
        )
      )
      .orderBy(desc(softwareRequests.createdAt))
      .limit(1)

    if (!existingRequest) {
      return c.json({ error: 'No demo request found to upgrade' }, 404)
    }

    if (existingRequest.status !== 'activated' && existingRequest.status !== 'approved') {
      return c.json({
        error: 'Demo request must be approved or activated before upgrading to production'
      }, 400)
    }

    // Calculate average monthly fee
    const fees = [data.class1Fee, data.class4Fee, data.class6Fee, data.class10Fee, data.class1112Fee]
    const calculatedAverageFee = fees.reduce((sum, fee) => sum + fee, 0) / fees.length

    // Get client IP and user agent for audit trail
    const clientIP = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
    const userAgent = c.req.header('user-agent') || 'unknown'

    // Start transaction
    await db.transaction(async (tx) => {
      // Update existing request to production
      await tx
        .update(softwareRequests)
        .set({
          requestType: 'production',
          class1Fee: data.class1Fee.toString(),
          class4Fee: data.class4Fee.toString(),
          class6Fee: data.class6Fee.toString(),
          class10Fee: data.class10Fee.toString(),
          class1112Fee: data.class1112Fee.toString(),
          calculatedAverageFee: calculatedAverageFee.toString(),
          termsAccepted: data.termsAccepted,
          termsAcceptedAt: new Date(),
          termsVersion: data.termsVersion,
          ipAddress: clientIP,
          userAgent: userAgent,
          status: 'pending', // Reset to pending for admin review
          updatedAt: new Date()
        })
        .where(eq(softwareRequests.id, existingRequest.id))

      // Add status history entry
      await tx.insert(requestStatusHistory).values({
        requestId: existingRequest.id,
        fromStatus: existingRequest.status,
        toStatus: 'pending',
        changedBy: user.id,
        changeReason: 'Upgraded from demo to production request',
        metadata: {
          upgradeType: 'demo_to_production',
          previousRequestType: 'demo',
          newRequestType: 'production',
          averageFee: calculatedAverageFee,
          clientIP,
          userAgent
        }
      })
    })

    return c.json({
      success: true,
      message: 'Successfully upgraded to production request. Your request is now pending admin review.',
      requestId: existingRequest.id,
      newStatus: 'pending'
    })

  } catch (error: any) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    console.error('Error upgrading to production:', error)
    return c.json({ error: 'Failed to upgrade to production' }, 500)
  }
})

// Get active terms & conditions endpoint
app.get('/terms-conditions', async (c) => {
  try {
    const [activeTerms] = await db
      .select()
      .from(termsConditions)
      .where(eq(termsConditions.isActive, true))
      .orderBy(termsConditions.effectiveDate)
      .limit(1)

    if (!activeTerms) {
      return c.json({ error: 'No active terms and conditions found' }, 404)
    }

    return c.json({
      version: activeTerms.version,
      title: activeTerms.title,
      content: activeTerms.content,
      effectiveDate: activeTerms.effectiveDate
    })

  } catch (error: any) {
    console.error('Error getting terms and conditions:', error)
    return c.json({ error: 'Failed to get terms and conditions' }, 500)
  }
})

export default app
