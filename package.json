{"name": "schopio-landing-page", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "dependencies": {"@google/genai": "^1.8.0", "@hono/node-server": "^1.13.7", "@hono/zod-validator": "^0.7.0", "@neondatabase/serverless": "^0.10.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.59.20", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.0.1", "drizzle-orm": "^0.36.4", "framer-motion": "^11.11.17", "hono": "^4.6.12", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "15.3.4", "razorpay": "^2.9.4", "react": "^19.0.0", "react-dom": "^19.0.0", "resend": "^4.6.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "drizzle-kit": "^0.28.1", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.4.49", "tailwindcss": "3.4.17", "typescript": "^5"}}