'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { 
  ArrowLeft, 
  DollarSign, 
  Calculator, 
  FileText, 
  CheckCircle, 
  AlertCircle,
  Loader2
} from 'lucide-react'

interface UpgradeFormData {
  class1Fee: number
  class4Fee: number
  class6Fee: number
  class10Fee: number
  class1112Fee: number
  termsAccepted: boolean
  termsVersion: string
}

interface TermsConditions {
  version: string
  title: string
  content: string
  effectiveDate: string
}

interface UpgradeToProductionFormProps {
  onSubmit: (data: UpgradeFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export default function UpgradeToProductionForm({ onSubmit, onCancel, isLoading = false }: UpgradeToProductionFormProps) {
  const [formData, setFormData] = useState<UpgradeFormData>({
    class1Fee: 0,
    class4Fee: 0,
    class6Fee: 0,
    class10Fee: 0,
    class1112Fee: 0,
    termsAccepted: false,
    termsVersion: ''
  })

  const [termsConditions, setTermsConditions] = useState<TermsConditions | null>(null)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [loadingTerms, setLoadingTerms] = useState(true)

  // Fetch terms and conditions
  useEffect(() => {
    const fetchTerms = async () => {
      try {
        const response = await fetch('/api/auth/terms-conditions')
        if (response.ok) {
          const terms = await response.json()
          setTermsConditions(terms)
          setFormData(prev => ({ ...prev, termsVersion: terms.version }))
        }
      } catch (error) {
        console.error('Error fetching terms:', error)
      } finally {
        setLoadingTerms(false)
      }
    }

    fetchTerms()
  }, [])

  const calculateAverageFee = () => {
    const fees = [formData.class1Fee, formData.class4Fee, formData.class6Fee, formData.class10Fee, formData.class1112Fee]
    const validFees = fees.filter(fee => fee > 0)
    return validFees.length > 0 ? validFees.reduce((sum, fee) => sum + fee, 0) / validFees.length : 0
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (formData.class1Fee <= 0) newErrors.class1Fee = 'Class 1 fee must be greater than 0'
    if (formData.class4Fee <= 0) newErrors.class4Fee = 'Class 4 fee must be greater than 0'
    if (formData.class6Fee <= 0) newErrors.class6Fee = 'Class 6 fee must be greater than 0'
    if (formData.class10Fee <= 0) newErrors.class10Fee = 'Class 10 fee must be greater than 0'
    if (formData.class1112Fee <= 0) newErrors.class1112Fee = 'Class 11-12 fee must be greater than 0'
    if (!formData.termsAccepted) newErrors.termsAccepted = 'You must accept the terms and conditions'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    await onSubmit(formData)
  }

  const handleInputChange = (field: keyof UpgradeFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  if (loadingTerms) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-gray-600">Loading upgrade form...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-4xl mx-auto space-y-6"
    >
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onCancel}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Upgrade to Production
              </CardTitle>
              <CardDescription>
                Complete your monthly fee structure to upgrade from demo to full production subscription
              </CardDescription>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Fee Structure Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Fee Structure
          </CardTitle>
          <CardDescription>
            Enter the annual fee for each class level in your school
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {[
                { key: 'class1Fee', label: 'Class 1 Annual Fee', placeholder: 'Enter Class 1 fee' },
                { key: 'class4Fee', label: 'Class 4 Annual Fee', placeholder: 'Enter Class 4 fee' },
                { key: 'class6Fee', label: 'Class 6 Annual Fee', placeholder: 'Enter Class 6 fee' },
                { key: 'class10Fee', label: 'Class 10 Annual Fee', placeholder: 'Enter Class 10 fee' },
                { key: 'class1112Fee', label: 'Class 11-12 Annual Fee', placeholder: 'Enter Class 11-12 fee' }
              ].map(({ key, label, placeholder }) => (
                <div key={key} className="space-y-2">
                  <Label htmlFor={key}>{label}</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id={key}
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder={placeholder}
                      value={formData[key as keyof UpgradeFormData] as number || 0}
                      onChange={(e) => handleInputChange(key as keyof UpgradeFormData, parseFloat(e.target.value) || 0)}
                      className={`pl-10 ${errors[key] ? 'border-red-500' : ''}`}
                    />
                  </div>
                  {errors[key] && (
                    <p className="text-sm text-red-600">{errors[key]}</p>
                  )}
                </div>
              ))}
            </div>

            {/* Average Fee Display */}
            {calculateAverageFee() > 0 && (
              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <Calculator className="h-5 w-5 text-blue-600" />
                  <span className="font-medium text-blue-900">Calculated Average Fee:</span>
                  <span className="text-lg font-bold text-blue-900">
                    ₹{calculateAverageFee().toFixed(2)}
                  </span>
                </div>
                <p className="text-sm text-blue-700 mt-1">
                  This will be used for subscription billing calculations
                </p>
              </div>
            )}

            <Separator />

            {/* Terms and Conditions */}
            {termsConditions && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Terms & Conditions
                </h3>
                
                <div className="max-h-60 overflow-y-auto p-4 border rounded-lg bg-gray-50">
                  <h4 className="font-medium mb-2">{termsConditions.title}</h4>
                  <div className="text-sm text-gray-700 whitespace-pre-wrap">
                    {termsConditions.content}
                  </div>
                  <p className="text-xs text-gray-500 mt-4">
                    Version: {termsConditions.version} | Effective: {new Date(termsConditions.effectiveDate).toLocaleDateString()}
                  </p>
                </div>

                <div className="flex items-start gap-3">
                  <input
                    type="checkbox"
                    id="termsAccepted"
                    checked={formData.termsAccepted}
                    onChange={(e) => handleInputChange('termsAccepted', e.target.checked)}
                    className="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <Label htmlFor="termsAccepted" className="text-sm leading-relaxed">
                    I have read and agree to the terms and conditions for production subscription
                  </Label>
                </div>
                {errors.termsAccepted && (
                  <p className="text-sm text-red-600">{errors.termsAccepted}</p>
                )}
              </div>
            )}

            {/* Submit Button */}
            <div className="flex justify-end gap-4 pt-6">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onCancel}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={isLoading || !formData.termsAccepted}
                className="bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Upgrading...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Upgrade to Production
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  )
}
